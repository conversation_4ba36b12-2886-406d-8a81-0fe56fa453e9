package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
)

// TaskFrequency represents how often a task can be completed
type TaskFrequency string

const (
	FrequencyDaily       TaskFrequency = "DAILY"
	FrequencyOneTime     TaskFrequency = "ONE_TIME"
	FrequencyUnlimited   TaskFrequency = "UNLIMITED"
	FrequencyProgressive TaskFrequency = "PROGRESSIVE"
	FrequencyManual      TaskFrequency = "MANUAL"
)

// ResetPeriod represents when a task should be reset
type ResetPeriod string

const (
	ResetDaily   ResetPeriod = "DAILY"
	ResetWeekly  ResetPeriod = "WEEKLY"
	ResetMonthly ResetPeriod = "MONTHLY"
	ResetNever   ResetPeriod = "NEVER"
)

// VerificationMethod represents how a task is verified
type VerificationMethod string

const (
	VerificationAuto        VerificationMethod = "AUTO"
	VerificationManual      VerificationMethod = "MANUAL"
	VerificationClickVerify VerificationMethod = "CLICK_VERIFY"
)

// TaskConditions represents the conditions required to complete a task
type TaskConditions struct {
	MinTradingVolume   *float64               `json:"min_trading_volume,omitempty"`
	RequiredTradeCount *int                   `json:"required_trade_count,omitempty"`
	ConsecutiveDays    *int                   `json:"consecutive_days,omitempty"`
	TargetPage         *string                `json:"target_page,omitempty"`
	SocialMediaAction  *string                `json:"social_media_action,omitempty"`
	ReferralCount      *int                   `json:"referral_count,omitempty"`
	CustomConditions   map[string]interface{} `json:"custom_conditions,omitempty"`
}

// Scan implements the sql.Scanner interface for TaskConditions
func (tc *TaskConditions) Scan(value interface{}) error {
	if value == nil {
		*tc = TaskConditions{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, tc)
}

// Value implements the driver.Valuer interface for TaskConditions
func (tc TaskConditions) Value() (driver.Value, error) {
	return json.Marshal(tc)
}

// ActivityTask represents the activity_tasks table
type ActivityTask struct {
	ID          uuid.UUID `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CategoryID  uint      `gorm:"not null" json:"category_id"`
	Name        string    `gorm:"type:varchar(100);not null" json:"name"` // Default English name
	NameCN      *string   `gorm:"type:varchar(100)" json:"name_cn"`       // Chinese name
	NameVN      *string   `gorm:"type:varchar(100)" json:"name_vn"`       // Vietnamese name
	Description *string   `gorm:"type:text" json:"description"`

	Frequency          TaskFrequency       `gorm:"type:varchar(20);not null" json:"frequency"`
	TaskIdentifier     *TaskIdentifier     `gorm:"type:varchar(50);index" json:"task_identifier"` // New field for unique identification
	Points             int                 `gorm:"not null;default:0" json:"points"`
	MaxCompletions     *int                `json:"max_completions"` // NULL for unlimited tasks
	ResetPeriod        *ResetPeriod        `gorm:"type:varchar(20)" json:"reset_period"`
	Conditions         *TaskConditions     `gorm:"type:jsonb" json:"conditions"`
	ActionTarget       *string             `gorm:"type:varchar(255)" json:"action_target"`
	VerificationMethod *VerificationMethod `gorm:"type:varchar(50)" json:"verification_method"`
	ExternalLink       *string             `gorm:"type:varchar(500)" json:"external_link"`
	TaskIcon           *string             `gorm:"type:varchar(255)" json:"task_icon"`   // New field for task icon
	ButtonText         *string             `gorm:"type:varchar(100)" json:"button_text"` // New field for button text
	IsActive           bool                `gorm:"default:true" json:"is_active"`
	StartDate          *time.Time          `json:"start_date"`
	EndDate            *time.Time          `json:"end_date"`
	SortOrder          int                 `gorm:"default:0" json:"sort_order"`
	CreatedAt          time.Time           `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt          time.Time           `gorm:"default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedBy          *uuid.UUID          `gorm:"type:uuid" json:"created_by"`
	UpdatedBy          *uuid.UUID          `gorm:"type:uuid" json:"updated_by"`

	// Relationships
	Category     TaskCategory       `gorm:"foreignKey:CategoryID;references:ID" json:"category,omitempty"`
	UserProgress []UserTaskProgress `gorm:"foreignKey:TaskID;references:ID" json:"user_progress,omitempty"`
}

// TableName specifies the table name for ActivityTask
func (ActivityTask) TableName() string {
	return "activity_tasks"
}

// IsExpired checks if the task has expired
func (at *ActivityTask) IsExpired() bool {
	if at.EndDate == nil {
		return false
	}
	return time.Now().After(*at.EndDate)
}

// IsStarted checks if the task has started
func (at *ActivityTask) IsStarted() bool {
	if at.StartDate == nil {
		return true
	}
	return time.Now().After(*at.StartDate)
}

// IsAvailable checks if the task is currently available
func (at *ActivityTask) IsAvailable() bool {
	return at.IsActive && at.IsStarted() && !at.IsExpired()
}
