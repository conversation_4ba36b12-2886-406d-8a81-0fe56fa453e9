# Activity Cashback Schema

# Enums
enum TaskCategoryName {
  DAILY
  COMMUNITY
  TRADING
}



enum TaskFrequency {
  DAILY
  ONE_TIME
  UNLIMITED
  PROGRESSIVE
  MANUAL
}

enum TaskIdentifier {
  # Daily Tasks
  DAILY_CHECKIN
  MEME_TRADE_DAILY
  PERPETUAL_TRADE_DAILY
  MARKET_PAGE_VIEW
  CONSECUTIVE_CHECKIN
  CONSECUTIVE_TRADING_DAYS

  # Community Tasks
  TWITTER_FOLLOW
  TWITTER_RETWEET
  TWITTER_LIKE
  TELEGRAM_JOIN
  INVITE_FRIENDS
  SHARE_REFERRAL

  # Trading Tasks
  TRADING_POINTS
  ACCUMULATED_TRADING_10K
  ACCUMULATED_TRADING_50K
  ACCUMULATED_TRADING_100K
  ACCUMULATED_TRADING_500K
}

enum TaskStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  CLAIMED
  EXPIRED
}

enum ClaimType {
  TRADING_CASHBACK
  TASK_REWARD
  TIER_BONUS
  REFERRAL_BONUS
}

enum ClaimStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

# Types
type TaskCategory {
  id: ID!
  name: TaskCategoryName!
  displayName: String!
  description: String
  icon: String
  sortOrder: Int!
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
  # tasks field removed to prevent circular reference with ActivityTask.category
}

type ActivityTask {
  id: ID!
  categoryId: ID!
  name: String!
  nameCn: String # Chinese name
  nameVn: String # Vietnamese name
  description: String

  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier # New field for unique task identification
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  taskIcon: String # New field for task icon
  buttonText: String # New field for button text
  isActive: Boolean!
  startDate: Int64
  endDate: Int64
  sortOrder: Int!
  createdAt: Time!
  updatedAt: Time!
  # category field removed to prevent circular reference with TaskCategory.tasks
}

type UserTaskProgress {
  id: ID!
  userId: ID!
  taskId: ID!
  status: TaskStatus!
  progressValue: Int!
  targetValue: Int
  completionCount: Int!
  pointsEarned: Int!
  lastCompletedAt: Time
  lastResetAt: Time
  streakCount: Int!
  metadata: String # JSON string
  createdAt: Time!
  updatedAt: Time!
  progressPercentage: Float!
  canBeClaimed: Boolean!
}

type UserTierInfo {
  userId: ID!
  currentTier: Int!
  totalPoints: Int!
  pointsThisMonth: Int!
  tradingVolumeUsd: Float!
  activeDaysThisMonth: Int!
  cumulativeCashbackUsd: Float!
  claimableCashbackUsd: Float!
  claimedCashbackUsd: Float!
  lastActivityDate: Time
  tierUpgradedAt: Time
  monthlyResetAt: Time
  createdAt: Time!
  updatedAt: Time!
  tierBenefit: TierBenefit
  userRank: Int
}

type TierBenefit {
  id: ID!
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  netFee: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean!
  createdAt: Time!
  updatedAt: Time!
}

type ActivityCashbackClaim {
  id: ID!
  userId: ID!
  claimType: ClaimType!
  totalAmountUsd: Float!
  totalAmountSol: Float!
  transactionHash: String
  status: ClaimStatus!
  claimedAt: Time!
  processedAt: Time
  metadata: String # JSON string
  createdAt: Time!
  updatedAt: Time!
}

type TaskCompletionHistory {
  id: ID!
  userId: ID!
  taskId: ID!
  pointsAwarded: Int!
  completionDate: Time!
  verificationData: String # JSON string
  createdAt: Time!
  task: ActivityTask
}

type UserDashboard {
  userTierInfo: UserTierInfo!
  tierBenefit: TierBenefit!
  nextTier: TierBenefit
  pointsToNextTier: Int!
  claimableCashback: Float!
  recentClaims: [ActivityCashbackClaim!]!
  userRank: Int!
}

type TaskCenter {
  categories: [TaskCategoryWithTasks!]!
  userProgress: [UserTaskProgress!]!
  completedToday: Int!
  pointsEarnedToday: Int!
  streakTasks: [UserTaskProgress!]!
}

type TaskCategoryWithTasks {
  category: TaskCategory!
  tasks: [TaskWithProgress!]!
}

type TaskWithProgress {
  task: ActivityTask!
  progress: UserTaskProgress
}



# Activity Cashback Summary for UI
type ActivityCashbackSummary {
  # Current ranking info
  currentLevel: Int!
  currentLevelName: String!
  nextLevel: Int
  nextLevelName: String

  # Progress calculation
  currentScore: Int!
  totalScoreForNextLevel: Int
  scoreRequiredToUpgrade: Int
  progressPercentage: Float!

  # Trading volume (MEME only for now)
  accumulatedTradingVolumeUsd: Float!

  # Activity tracking
  activeLogonDays: Int!

  # Cashback information
  accumulatedCashbackUsd: Float!
  claimableCashbackUsd: Float!
  claimedCashbackUsd: Float!

  # Additional tier info
  currentTierColor: String
  currentTierIcon: String
  nextTierColor: String
  nextTierIcon: String
}

type ActivityCashbackSummaryResponse {
  success: Boolean!
  message: String!
  data: ActivityCashbackSummary
}

# Response Types
type UserDashboardResponse {
  success: Boolean!
  message: String!
  data: UserDashboard
}

type TaskCenterResponse {
  success: Boolean!
  message: String!
  data: TaskCenter
}



type TaskCompletionResponse {
  success: Boolean!
  message: String!
  pointsAwarded: Int!
  newTierLevel: Int
  tierUpgraded: Boolean!
  isPending: Boolean!
  remainingWaitTimeSeconds: Int
  completionTime: Time
}

type TaskClaimResponse {
  success: Boolean!
  message: String!
  pointsClaimed: Int!
}

type CashbackClaimResponse {
  success: Boolean!
  message: String!
  claimId: ID!
  amountUsd: Float!
  amountSol: Float!
}

type TierBenefitsResponse {
  success: Boolean!
  message: String!
  data: [TierBenefit!]!
}

type TierBenefitResponse {
  success: Boolean!
  message: String!
  data: TierBenefit
}

type UserTaskProgressResponse {
  success: Boolean!
  message: String!
  data: [UserTaskProgress!]!
}

type UserTaskListByCategoryResponse {
  success: Boolean!
  message: String!
  data: [TaskWithProgress!]!
}

type TaskCompletionHistoryResponse {
  success: Boolean!
  message: String!
  data: [TaskCompletionHistory!]!
  total: Int!
}

# Input Types
input CompleteTaskInput {
  taskId: ID!
  verificationData: String # JSON string
}

input ClaimTaskRewardInput {
  taskId: ID!
}

input ClaimCashbackInput {
  amountUsd: Float!
}

input UserTaskListByCategoryInput {
  categoryName: TaskCategoryName!
}

input CreateTaskInput {
  categoryId: ID!
  name: String!
  description: String

  frequency: TaskFrequency!
  taskIdentifier: TaskIdentifier # Optional unique identifier for task processing
  points: Int!
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  startDate: Int64
  endDate: Int64
  sortOrder: Int
}

input UpdateTaskInput {
  id: ID!
  categoryId: ID
  name: String
  description: String

  frequency: TaskFrequency
  points: Int
  maxCompletions: Int
  resetPeriod: String
  conditions: String # JSON string
  actionTarget: String
  verificationMethod: String
  externalLink: String
  isActive: Boolean
  startDate: Int64
  endDate: Int64
  sortOrder: Int
}



input CreateTierBenefitInput {
  tierLevel: Int!
  tierName: String!
  minPoints: Int!
  cashbackPercentage: Float!
  netFee: Float!
  benefitsDescription: String
  tierColor: String
  tierIcon: String
}

input UpdateTierBenefitInput {
  id: ID!
  tierLevel: Int
  tierName: String
  minPoints: Int
  cashbackPercentage: Float
  netFee: Float
  benefitsDescription: String
  tierColor: String
  tierIcon: String
  isActive: Boolean
}

input TaskCompletionHistoryInput {
  taskId: ID
  startDate: Time
  endDate: Time
  limit: Int
  offset: Int
}



# Queries
extend type Query {
  # Get user dashboard for activity cashback
  activityCashbackDashboard: UserDashboardResponse! @auth

  # Get activity cashback summary for UI (optimized for frontend display)
  activityCashbackSummary: ActivityCashbackSummaryResponse! @auth

  # Get task center with all tasks and progress
  taskCenter: TaskCenterResponse! @auth

  # Get all tier benefits
  tierBenefits: TierBenefitsResponse! @auth

  # Get user task progress
  userTaskProgress: UserTaskProgressResponse! @auth

  # Get task completion history
  taskCompletionHistory(input: TaskCompletionHistoryInput): TaskCompletionHistoryResponse! @auth

  # Get user tier info
  userTierInfo: UserTierInfo @auth

  # Get task categories
  taskCategories: [TaskCategory!]! @auth

  # Get tasks by category
  tasksByCategory(categoryName: TaskCategoryName!): [ActivityTask!]! @auth

  # Get user task progress by category
  userTaskListByCategory(input: UserTaskListByCategoryInput!): UserTaskListByCategoryResponse! @auth


}

# Mutations
extend type Mutation {
  # Complete a task
  completeTask(input: CompleteTaskInput!): TaskCompletionResponse! @auth

  # Claim task reward
  claimTaskReward(input: ClaimTaskRewardInput!): TaskClaimResponse! @auth

  # Claim cashback
  claimCashback(input: ClaimCashbackInput!): CashbackClaimResponse! @auth

  # Refresh task list
  refreshTaskList: Boolean! @auth


}
