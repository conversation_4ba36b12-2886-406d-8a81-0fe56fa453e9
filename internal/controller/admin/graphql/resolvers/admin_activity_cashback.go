package resolvers

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// AdminActivityCashbackResolver handles admin-specific activity cashback operations
type AdminActivityCashbackResolver struct {
	adminService activity_cashback.AdminServiceInterface
}

// NewAdminActivityCashbackResolver creates a new AdminActivityCashbackResolver
func NewAdminActivityCashbackResolver() *AdminActivityCashbackResolver {
	return &AdminActivityCashbackResolver{
		adminService: activity_cashback.NewAdminService(),
	}
}

// CreateTask creates a new activity task (Admin only)
func (r *AdminActivityCashbackResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	// Parse CategoryID as uint
	categoryID, err := strconv.ParseUint(input.CategoryID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid category ID: %w", err)
	}

	// Create task model
	task := &model.ActivityTask{
		CategoryID: uint(categoryID),
		Name:       input.Name,

		Frequency: model.TaskFrequency(input.Frequency),
		Points:    input.Points,
		SortOrder: 0, // Default value
		IsActive:  true,
	}

	// Set optional fields
	if input.Description != nil {
		task.Description = input.Description
	}
	if input.NameCn != nil {
		task.NameCN = input.NameCn
	}
	if input.NameVn != nil {
		task.NameVN = input.NameVn
	}
	if input.TaskIdentifier != nil {
		taskIdentifier := model.TaskIdentifier(*input.TaskIdentifier)
		task.TaskIdentifier = &taskIdentifier
	}
	if input.MaxCompletions != nil {
		task.MaxCompletions = input.MaxCompletions
	}
	if input.ResetPeriod != nil {
		resetPeriod := model.ResetPeriod(*input.ResetPeriod)
		task.ResetPeriod = &resetPeriod
	}
	if input.Conditions != nil {
		var conditions model.TaskConditions
		if err := json.Unmarshal([]byte(*input.Conditions), &conditions); err != nil {
			return nil, fmt.Errorf("invalid conditions format: %w", err)
		}
		task.Conditions = &conditions
	}
	if input.ActionTarget != nil {
		task.ActionTarget = input.ActionTarget
	}
	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		task.VerificationMethod = &verificationMethod
	}
	if input.ExternalLink != nil {
		task.ExternalLink = input.ExternalLink
	}
	if input.TaskIcon != nil {
		task.TaskIcon = input.TaskIcon
	}
	if input.ButtonText != nil {
		task.ButtonText = input.ButtonText
	}
	if input.StartDate != nil {
		task.StartDate = utils.TimestampToTime(input.StartDate)
	}
	if input.EndDate != nil {
		task.EndDate = utils.TimestampToTime(input.EndDate)
	}
	if input.SortOrder != nil {
		task.SortOrder = *input.SortOrder
	}

	// Get admin ID from context (assuming it's set by middleware)
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	adminID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	// Create task using admin service
	err = r.adminService.CreateTask(ctx, task, adminID)
	if err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err))
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	// Load the category to populate the category field
	categories, err := r.adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	// Find the category for this task
	var taskCategory *model.TaskCategory
	for _, cat := range categories {
		if cat.ID == task.CategoryID {
			taskCategory = &cat
			break
		}
	}

	if taskCategory == nil {
		return nil, fmt.Errorf("category not found for task")
	}

	// Convert to GraphQL model with category
	gqlTask := convertActivityTaskToGQL(task)
	gqlTask.Category = convertTaskCategoryToGQL(taskCategory)

	return gqlTask, nil
}

// UpdateTask updates an existing activity task (Admin only)
func (r *AdminActivityCashbackResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	taskID := uuid.MustParse(input.ID)

	// Get existing task using GetAllTasks and filter by ID
	allTasks, err := r.adminService.GetAllTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tasks for update", zap.Error(err))
		return nil, fmt.Errorf("failed to get tasks: %w", err)
	}

	var existingTask *model.ActivityTask
	for _, task := range allTasks {
		if task.ID == taskID {
			existingTask = &task
			break
		}
	}

	if existingTask == nil {
		return nil, fmt.Errorf("task not found")
	}

	// Update fields if provided
	if input.CategoryID != nil {
		categoryID, err := strconv.ParseUint(*input.CategoryID, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid category ID: %w", err)
		}
		existingTask.CategoryID = uint(categoryID)
	}
	if input.Name != nil {
		existingTask.Name = *input.Name
	}
	if input.NameCn != nil {
		existingTask.NameCN = input.NameCn
	}
	if input.NameVn != nil {
		existingTask.NameVN = input.NameVn
	}
	if input.Description != nil {
		existingTask.Description = input.Description
	}

	if input.Frequency != nil {
		existingTask.Frequency = model.TaskFrequency(*input.Frequency)
	}
	if input.TaskIdentifier != nil {
		taskIdentifier := model.TaskIdentifier(*input.TaskIdentifier)
		existingTask.TaskIdentifier = &taskIdentifier
	}
	if input.Points != nil {
		existingTask.Points = *input.Points
	}
	if input.MaxCompletions != nil {
		existingTask.MaxCompletions = input.MaxCompletions
	}
	if input.ResetPeriod != nil {
		resetPeriod := model.ResetPeriod(*input.ResetPeriod)
		existingTask.ResetPeriod = &resetPeriod
	}
	if input.Conditions != nil {
		var conditions model.TaskConditions
		if err := json.Unmarshal([]byte(*input.Conditions), &conditions); err != nil {
			return nil, fmt.Errorf("invalid conditions format: %w", err)
		}
		existingTask.Conditions = &conditions
	}
	if input.ActionTarget != nil {
		existingTask.ActionTarget = input.ActionTarget
	}
	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		existingTask.VerificationMethod = &verificationMethod
	}
	if input.ExternalLink != nil {
		existingTask.ExternalLink = input.ExternalLink
	}
	if input.TaskIcon != nil {
		existingTask.TaskIcon = input.TaskIcon
	}
	if input.ButtonText != nil {
		existingTask.ButtonText = input.ButtonText
	}
	if input.StartDate != nil {
		existingTask.StartDate = utils.TimestampToTime(input.StartDate)
	}
	if input.EndDate != nil {
		existingTask.EndDate = utils.TimestampToTime(input.EndDate)
	}
	if input.SortOrder != nil {
		existingTask.SortOrder = *input.SortOrder
	}
	if input.IsActive != nil {
		existingTask.IsActive = *input.IsActive
	}

	// Get admin ID from context
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	adminID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	// Update task using admin service
	err = r.adminService.UpdateTask(ctx, existingTask, adminID)
	if err != nil {
		global.GVA_LOG.Error("Failed to update task", zap.Error(err))
		return nil, fmt.Errorf("failed to update task: %w", err)
	}

	return convertActivityTaskToGQL(existingTask), nil
}

// DeleteTask deletes an activity task (Admin only)
func (r *AdminActivityCashbackResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	taskUUID := uuid.MustParse(taskID)

	err := r.adminService.DeleteTask(ctx, taskUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to delete task", zap.Error(err))
		return false, fmt.Errorf("failed to delete task: %w", err)
	}

	return true, nil
}

// CreateTaskCategory creates a new task category (Admin only)
func (r *AdminActivityCashbackResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	// Convert admin GraphQL enum to model enum
	modelCategoryName := convertAdminGQLCategoryToModel(input.Name)

	// Create category model
	category := &model.TaskCategory{
		Name:        modelCategoryName,
		DisplayName: input.DisplayName,
		IsActive:    true,
		SortOrder:   0, // Default value
	}

	// Set optional fields
	if input.Description != nil {
		category.Description = input.Description
	}

	if input.Icon != nil {
		category.Icon = input.Icon
	}

	if input.SortOrder != nil {
		category.SortOrder = *input.SortOrder
	}

	// Create category using admin service
	err := r.adminService.CreateTaskCategory(ctx, category)
	if err != nil {
		global.GVA_LOG.Error("Failed to create task category", zap.Error(err))
		return nil, fmt.Errorf("failed to create task category: %w", err)
	}

	return convertTaskCategoryToGQL(category), nil
}

// UpdateTaskCategory updates an existing task category (Admin only)
func (r *AdminActivityCashbackResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	// Parse category ID as uint
	categoryIDUint, err := strconv.ParseUint(input.ID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid category ID: %w", err)
	}

	// Get existing category using GetTaskCategories and filter by ID
	allCategories, err := r.adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories for update", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	var existingCategory *model.TaskCategory
	for _, category := range allCategories {
		if category.ID == uint(categoryIDUint) {
			existingCategory = &category
			break
		}
	}

	if existingCategory == nil {
		return nil, fmt.Errorf("task category not found")
	}

	// Update fields if provided
	if input.Name != nil {
		existingCategory.Name = convertAdminGQLCategoryToModel(*input.Name)
	}
	if input.DisplayName != nil {
		existingCategory.DisplayName = *input.DisplayName
	}
	if input.Description != nil {
		existingCategory.Description = input.Description
	}
	if input.Icon != nil {
		existingCategory.Icon = input.Icon
	}
	if input.IsActive != nil {
		existingCategory.IsActive = *input.IsActive
	}
	if input.SortOrder != nil {
		existingCategory.SortOrder = *input.SortOrder
	}

	// Update category using admin service
	err = r.adminService.UpdateTaskCategory(ctx, existingCategory)
	if err != nil {
		global.GVA_LOG.Error("Failed to update task category", zap.Error(err))
		return nil, fmt.Errorf("failed to update task category: %w", err)
	}

	return convertTaskCategoryToGQL(existingCategory), nil
}

// DeleteTaskCategory deletes a task category (Admin only)
func (r *AdminActivityCashbackResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	// Parse category ID as uint
	categoryIDUint, err := strconv.ParseUint(categoryID, 10, 32)
	if err != nil {
		return false, fmt.Errorf("invalid category ID: %w", err)
	}

	err = r.adminService.DeleteTaskCategory(ctx, uint(categoryIDUint))
	if err != nil {
		global.GVA_LOG.Error("Failed to delete task category", zap.Error(err))
		return false, fmt.Errorf("failed to delete task category: %w", err)
	}

	return true, nil
}

// CreateTierBenefit creates a new tier benefit (Admin only)
func (r *AdminActivityCashbackResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	// Create tier benefit model
	tierBenefit := &model.TierBenefit{
		TierLevel:          input.TierLevel,
		TierName:           input.TierName,
		MinPoints:          input.MinPoints,
		CashbackPercentage: decimal.NewFromFloat(input.CashbackPercentage),
		NetFee:             decimal.NewFromFloat(input.NetFee),
		IsActive:           true,
	}

	// Set optional fields
	if input.BenefitsDescription != nil {
		tierBenefit.BenefitsDescription = input.BenefitsDescription
	}
	if input.TierColor != nil {
		tierBenefit.TierColor = input.TierColor
	}
	if input.TierIcon != nil {
		tierBenefit.TierIcon = input.TierIcon
	}

	// Create tier benefit using admin service
	err := r.adminService.CreateTierBenefit(ctx, tierBenefit)
	if err != nil {
		global.GVA_LOG.Error("Failed to create tier benefit", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create tier benefit: %v", err),
		}, nil
	}

	return &gql_model.TierBenefitResponse{
		Success: true,
		Message: "Tier benefit created successfully",
		Data:    convertTierBenefitToGQL(tierBenefit),
	}, nil
}

// Helper functions to convert model to GraphQL types
func convertActivityTaskToGQL(task *model.ActivityTask) *gql_model.ActivityTask {
	gqlTask := &gql_model.ActivityTask{
		ID:   task.ID.String(),
		Name: task.Name,

		Frequency: gql_model.TaskFrequency(task.Frequency),
		Points:    task.Points,
		SortOrder: task.SortOrder,
		IsActive:  task.IsActive,
		CreatedAt: task.CreatedAt,
		UpdatedAt: task.UpdatedAt,
	}

	if task.CategoryID != 0 {
		gqlTask.CategoryID = strconv.FormatUint(uint64(task.CategoryID), 10)
	}

	if task.Description != nil {
		gqlTask.Description = task.Description
	}

	if task.TaskIdentifier != nil {
		taskIdentifier := gql_model.TaskIdentifier(*task.TaskIdentifier)
		gqlTask.TaskIdentifier = &taskIdentifier
	}

	if task.MaxCompletions != nil {
		gqlTask.MaxCompletions = task.MaxCompletions
	}

	if task.ResetPeriod != nil {
		resetPeriodStr := string(*task.ResetPeriod)
		gqlTask.ResetPeriod = &resetPeriodStr
	}

	if task.Conditions != nil {
		conditionsBytes, _ := json.Marshal(task.Conditions)
		conditionsStr := string(conditionsBytes)
		gqlTask.Conditions = &conditionsStr
	}

	if task.ActionTarget != nil {
		gqlTask.ActionTarget = task.ActionTarget
	}

	if task.VerificationMethod != nil {
		verificationMethodStr := string(*task.VerificationMethod)
		gqlTask.VerificationMethod = &verificationMethodStr
	}

	if task.ExternalLink != nil {
		gqlTask.ExternalLink = task.ExternalLink
	}

	if task.TaskIcon != nil {
		gqlTask.TaskIcon = task.TaskIcon
	}

	if task.ButtonText != nil {
		gqlTask.ButtonText = task.ButtonText
	}

	if task.StartDate != nil {
		gqlTask.StartDate = utils.TimeToTimestamp(task.StartDate)
	}

	if task.EndDate != nil {
		gqlTask.EndDate = utils.TimeToTimestamp(task.EndDate)
	}

	// Convert category relationship if available
	if task.Category.ID != 0 {
		gqlTask.Category = convertTaskCategoryToGQL(&task.Category)
	}

	return gqlTask
}

func convertTaskCategoryToGQL(category *model.TaskCategory) *gql_model.TaskCategory {
	gqlCategory := &gql_model.TaskCategory{
		ID:          strconv.FormatUint(uint64(category.ID), 10),
		Name:        convertModelCategoryToAdminGQL(category.Name),
		DisplayName: category.DisplayName,
		IsActive:    category.IsActive,
		SortOrder:   category.SortOrder,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}

	if category.Description != nil {
		gqlCategory.Description = category.Description
	}

	if category.Icon != nil {
		gqlCategory.Icon = category.Icon
	}

	return gqlCategory
}

func convertTierBenefitToGQL(tierBenefit *model.TierBenefit) *gql_model.TierBenefit {
	cashbackPercentage, _ := tierBenefit.CashbackPercentage.Float64()
	netFee, _ := tierBenefit.NetFee.Float64()
	gqlTierBenefit := &gql_model.TierBenefit{
		ID:                 strconv.FormatUint(uint64(tierBenefit.ID), 10),
		TierLevel:          tierBenefit.TierLevel,
		TierName:           tierBenefit.TierName,
		MinPoints:          tierBenefit.MinPoints,
		CashbackPercentage: cashbackPercentage,
		NetFee:             netFee,
		IsActive:           tierBenefit.IsActive,
		CreatedAt:          tierBenefit.CreatedAt,
		UpdatedAt:          tierBenefit.UpdatedAt,
	}

	if tierBenefit.BenefitsDescription != nil {
		gqlTierBenefit.BenefitsDescription = tierBenefit.BenefitsDescription
	}

	if tierBenefit.TierColor != nil {
		gqlTierBenefit.TierColor = tierBenefit.TierColor
	}

	if tierBenefit.TierIcon != nil {
		gqlTierBenefit.TierIcon = tierBenefit.TierIcon
	}

	return gqlTierBenefit
}

// UpdateTierBenefit updates an existing tier benefit (Admin only)
func (r *AdminActivityCashbackResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	// Parse tier benefit ID as uint
	tierBenefitIDUint, err := strconv.ParseUint(input.ID, 10, 32)
	if err != nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid tier benefit ID",
		}, nil
	}

	// Get existing tier benefit using GetTierBenefits and filter by ID
	allTierBenefits, err := r.adminService.GetTierBenefits(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier benefits for update", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get tier benefits: %v", err),
		}, nil
	}

	var existingTierBenefit *model.TierBenefit
	for _, tierBenefit := range allTierBenefits {
		if tierBenefit.ID == uint(tierBenefitIDUint) {
			existingTierBenefit = &tierBenefit
			break
		}
	}

	if existingTierBenefit == nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Tier benefit not found",
		}, nil
	}

	// Update fields if provided
	if input.TierLevel != nil {
		existingTierBenefit.TierLevel = *input.TierLevel
	}
	if input.TierName != nil {
		existingTierBenefit.TierName = *input.TierName
	}
	if input.MinPoints != nil {
		existingTierBenefit.MinPoints = *input.MinPoints
	}
	if input.CashbackPercentage != nil {
		existingTierBenefit.CashbackPercentage = decimal.NewFromFloat(*input.CashbackPercentage)
	}
	if input.NetFee != nil {
		existingTierBenefit.NetFee = decimal.NewFromFloat(*input.NetFee)
	}
	if input.BenefitsDescription != nil {
		existingTierBenefit.BenefitsDescription = input.BenefitsDescription
	}
	if input.TierColor != nil {
		existingTierBenefit.TierColor = input.TierColor
	}
	if input.TierIcon != nil {
		existingTierBenefit.TierIcon = input.TierIcon
	}
	if input.IsActive != nil {
		existingTierBenefit.IsActive = *input.IsActive
	}

	// Update tier benefit using admin service
	err = r.adminService.UpdateTierBenefit(ctx, existingTierBenefit)
	if err != nil {
		global.GVA_LOG.Error("Failed to update tier benefit", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update tier benefit: %v", err),
		}, nil
	}

	return &gql_model.TierBenefitResponse{
		Success: true,
		Message: "Tier benefit updated successfully",
		Data:    convertTierBenefitToGQL(existingTierBenefit),
	}, nil
}

// DeleteTierBenefit deletes a tier benefit (Admin only)
func (r *AdminActivityCashbackResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	// Parse tier benefit ID as uint
	tierBenefitIDUint, err := strconv.ParseUint(tierBenefitID, 10, 32)
	if err != nil {
		return false, fmt.Errorf("invalid tier benefit ID: %w", err)
	}

	err = r.adminService.DeleteTierBenefit(ctx, uint(tierBenefitIDUint))
	if err != nil {
		global.GVA_LOG.Error("Failed to delete tier benefit", zap.Error(err))
		return false, fmt.Errorf("failed to delete tier benefit: %w", err)
	}

	return true, nil
}

// AdminGetAllTasks retrieves all tasks (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	tasks, err := r.adminService.GetAllTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get all tasks", zap.Error(err))
		return nil, fmt.Errorf("failed to get all tasks: %w", err)
	}

	var gqlTasks []*gql_model.ActivityTask
	for _, task := range tasks {
		gqlTasks = append(gqlTasks, convertActivityTaskToGQL(&task))
	}

	return gqlTasks, nil
}

// AdminGetTaskCompletionStats retrieves task completion statistics (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	stats, err := r.adminService.GetTaskCompletionStats(ctx, input.StartDate, input.EndDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task completion stats", zap.Error(err))
		return &gql_model.AdminTaskCompletionStatsResponse{
			Success: false,
			Message: "Failed to retrieve task completion statistics",
		}, nil
	}

	// Convert stats to GraphQL format
	var taskCompletions []*gql_model.TaskCompletionStat
	if taskStats, ok := stats["task_completions"].(map[string]int); ok {
		for taskName, count := range taskStats {
			taskCompletions = append(taskCompletions, &gql_model.TaskCompletionStat{
				TaskName:        taskName,
				CompletionCount: count,
			})
		}
	}

	totalTasks := 0
	if total, ok := stats["total_tasks"].(int); ok {
		totalTasks = total
	}

	return &gql_model.AdminTaskCompletionStatsResponse{
		Success: true,
		Message: "Task completion statistics retrieved successfully",
		Data: &gql_model.AdminTaskCompletionStats{
			TaskCompletions: taskCompletions,
			StartDate:       input.StartDate,
			EndDate:         input.EndDate,
			TotalTasks:      totalTasks,
		},
	}, nil
}

// AdminGetUserActivityStats retrieves user activity statistics (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	stats, err := r.adminService.GetUserActivityStats(ctx, input.StartDate, input.EndDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user activity stats", zap.Error(err))
		return &gql_model.AdminUserActivityStatsResponse{
			Success: false,
			Message: "Failed to retrieve user activity statistics",
		}, nil
	}

	// Convert stats to GraphQL format
	var dailyCompletions []*gql_model.DailyCompletionStat
	if dailyStats, ok := stats["daily_completions"].(map[string]int); ok {
		for date, count := range dailyStats {
			dailyCompletions = append(dailyCompletions, &gql_model.DailyCompletionStat{
				Date:            date,
				CompletionCount: count,
			})
		}
	}

	return &gql_model.AdminUserActivityStatsResponse{
		Success: true,
		Message: "User activity statistics retrieved successfully",
		Data: &gql_model.AdminUserActivityStats{
			DailyCompletions: dailyCompletions,
			StartDate:        input.StartDate,
			EndDate:          input.EndDate,
		},
	}, nil
}

// AdminGetTierDistribution retrieves tier distribution statistics (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	distribution, err := r.adminService.GetTierDistribution(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier distribution", zap.Error(err))
		return &gql_model.AdminTierDistributionResponse{
			Success: false,
			Message: "Failed to retrieve tier distribution",
		}, nil
	}

	var tierStats []*gql_model.TierDistributionStat
	for tierLevel, userCount := range distribution {
		tierStats = append(tierStats, &gql_model.TierDistributionStat{
			TierLevel: tierLevel,
			UserCount: userCount,
		})
	}

	return &gql_model.AdminTierDistributionResponse{
		Success: true,
		Message: "Tier distribution retrieved successfully",
		Data:    tierStats,
	}, nil
}

// AdminGetTopUsers retrieves top users by points (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	limitValue := 10 // Default limit
	if limit != nil {
		limitValue = *limit
	}

	users, err := r.adminService.GetTopUsers(ctx, limitValue)
	if err != nil {
		global.GVA_LOG.Error("Failed to get top users", zap.Error(err))
		return nil, fmt.Errorf("failed to get top users: %w", err)
	}

	var gqlUsers []*gql_model.UserTierInfo
	for _, user := range users {
		// Convert decimal fields to float64
		availableCashback, _ := user.ClaimableCashbackUSD.Float64()
		totalCashbackClaimed, _ := user.ClaimedCashbackUSD.Float64()

		gqlUser := &gql_model.UserTierInfo{
			UserID:               user.UserID.String(),
			TotalPoints:          user.TotalPoints,
			AvailableCashback:    availableCashback,
			TotalCashbackClaimed: totalCashbackClaimed,
			CreatedAt:            user.CreatedAt,
		}

		// Set email from User relationship if available
		if user.User.Email != nil {
			gqlUser.Email = user.User.Email
		}

		// Set last activity date
		if user.LastActivityDate != nil {
			gqlUser.LastActivityAt = user.LastActivityDate
		}

		gqlUsers = append(gqlUsers, gqlUser)
	}

	return gqlUsers, nil
}

// Admin reset functions
func (r *AdminActivityCashbackResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	err := r.adminService.ResetAllDailyTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to reset daily tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset daily tasks: %w", err)
	}
	return true, nil
}

func (r *AdminActivityCashbackResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	err := r.adminService.ResetAllWeeklyTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to reset weekly tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset weekly tasks: %w", err)
	}
	return true, nil
}

func (r *AdminActivityCashbackResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	err := r.adminService.ResetAllMonthlyTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to reset monthly tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset monthly tasks: %w", err)
	}
	return true, nil
}

func (r *AdminActivityCashbackResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	err := r.adminService.RecalculateAllUserTiers(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to recalculate user tiers", zap.Error(err))
		return false, fmt.Errorf("failed to recalculate user tiers: %w", err)
	}
	return true, nil
}

func (r *AdminActivityCashbackResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	err := r.adminService.SeedInitialTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to seed initial tasks", zap.Error(err))
		return false, fmt.Errorf("failed to seed initial tasks: %w", err)
	}
	return true, nil
}

// Helper functions to convert between model and admin GraphQL category enums
func convertModelCategoryToAdminGQL(modelCategory model.TaskCategoryName) gql_model.TaskCategoryName {
	switch modelCategory {
	case model.CategoryDaily:
		return gql_model.TaskCategoryNameDaily
	case model.CategoryCommunity:
		return gql_model.TaskCategoryNameCommunity
	case model.CategoryTrading:
		return gql_model.TaskCategoryNameTrading
	default:
		return gql_model.TaskCategoryNameDaily
	}
}

func convertAdminGQLCategoryToModel(adminGQLCategory gql_model.TaskCategoryName) model.TaskCategoryName {
	switch adminGQLCategory {
	case gql_model.TaskCategoryNameDaily:
		return model.CategoryDaily
	case gql_model.TaskCategoryNameCommunity:
		return model.CategoryCommunity
	case gql_model.TaskCategoryNameTrading:
		return model.CategoryTrading
	default:
		return model.CategoryDaily
	}
}
