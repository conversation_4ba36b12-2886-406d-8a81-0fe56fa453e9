package activity_cashback

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/test"
	"go.uber.org/zap"
)

// TradingVolumeAccumulationTestSuite tests trading volume accumulation in user_tier_info
type TradingVolumeAccumulationTestSuite struct {
	suite.Suite
	ctx        context.Context
	service    ActivityCashbackServiceInterface
	testUserID uuid.UUID
	helper     *test.TestHelper
}

// SetupSuite initializes the test suite
func (suite *TradingVolumeAccumulationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testUserID = uuid.New()
	suite.helper = test.NewTestHelper(suite.T())

	// Setup logger to avoid nil pointer dereference
	logger, _ := zap.NewDevelopment()
	global.GVA_LOG = logger

	// Initialize service
	suite.service = NewActivityCashbackService()
}

// TestTradingVolumeAccumulation_CurrentIssue tests the current issue where trading volume is not accumulated
func (suite *TradingVolumeAccumulationTestSuite) TestTradingVolumeAccumulation_CurrentIssue() {
	// This test demonstrates the current issue: trading volume is not being accumulated in user_tier_info

	// Arrange: Initialize user for activity cashback
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, suite.testUserID)
	suite.NoError(err)

	// Get initial tier info
	initialTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, suite.testUserID)
	suite.NoError(err)
	suite.Equal(decimal.Zero, initialTierInfo.TradingVolumeUSD, "Initial trading volume should be 0")

	// Act: Simulate multiple trades through the current system
	trades := []map[string]interface{}{
		{
			"trade_type": "MEME",
			"volume":     100.0,
			"order_id":   uuid.New().String(),
			"user_id":    suite.testUserID.String(),
		},
		{
			"trade_type": "MEME",
			"volume":     250.0,
			"order_id":   uuid.New().String(),
			"user_id":    suite.testUserID.String(),
		},
		{
			"trade_type": "MEME",
			"volume":     50.0,
			"order_id":   uuid.New().String(),
			"user_id":    suite.testUserID.String(),
		},
	}

	// Process trades through TaskProcessorManager (current flow)
	taskProcessorManager := NewTaskProcessorManager(suite.service)
	for _, tradeData := range trades {
		err := taskProcessorManager.ProcessTradingEvent(suite.ctx, suite.testUserID, tradeData)
		suite.NoError(err, "Trade processing should succeed")
	}

	// Assert: Check if trading volume was accumulated (this will currently fail)
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, suite.testUserID)
	suite.NoError(err)

	expectedVolume := decimal.NewFromFloat(400.0) // 100 + 250 + 50
	suite.T().Logf("Expected trading volume: %s", expectedVolume.String())
	suite.T().Logf("Actual trading volume: %s", finalTierInfo.TradingVolumeUSD.String())

	// This assertion will currently FAIL because trading volume is not being accumulated
	if finalTierInfo.TradingVolumeUSD.Equal(decimal.Zero) {
		suite.T().Log("❌ ISSUE CONFIRMED: Trading volume is not being accumulated in user_tier_info")
		suite.T().Log("❌ Current system only processes tasks but doesn't update TradingVolumeUSD field")
	} else {
		suite.T().Log("✅ Trading volume is being accumulated correctly")
		suite.Equal(expectedVolume, finalTierInfo.TradingVolumeUSD, "Trading volume should be accumulated")
	}

	// Check ActivityCashbackSummary
	summary, err := suite.service.GetActivityCashbackSummary(suite.ctx, suite.testUserID)
	suite.NoError(err)

	suite.T().Logf("Summary AccumulatedTradingVolumeUSD: %s", summary.AccumulatedTradingVolumeUSD.String())

	if summary.AccumulatedTradingVolumeUSD.Equal(decimal.Zero) {
		suite.T().Log("❌ CONFIRMED: AccumulatedTradingVolumeUSD in summary is 0")
		suite.T().Log("❌ This explains why the frontend shows 0 for accumulatedTradingVolumeUsd")
	}
}

// TestTradingVolumeAccumulation_ProposedFix tests the proposed fix
func (suite *TradingVolumeAccumulationTestSuite) TestTradingVolumeAccumulation_ProposedFix() {
	// This test demonstrates how the fix should work

	// Arrange: Initialize user
	testUserID := uuid.New()
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err)

	// Get initial tier info
	initialTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)
	suite.Equal(decimal.Zero, initialTierInfo.TradingVolumeUSD)

	// Act: Manually add trading volume using the AddTradingVolume method
	// This is what should happen automatically when trades are processed
	volumes := []decimal.Decimal{
		decimal.NewFromFloat(100.0),
		decimal.NewFromFloat(250.0),
		decimal.NewFromFloat(50.0),
	}

	for _, volume := range volumes {
		// Get current tier info
		tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
		suite.NoError(err)

		// Add trading volume (this is what's missing in current implementation)
		tierInfo.AddTradingVolume(volume)

		// Update tier info
		err = suite.service.UpdateUserTierInfo(suite.ctx, tierInfo)
		suite.NoError(err)
	}

	// Assert: Check if trading volume was accumulated correctly
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)

	expectedVolume := decimal.NewFromFloat(400.0)
	suite.Equal(expectedVolume, finalTierInfo.TradingVolumeUSD, "Trading volume should be accumulated with the fix")

	// Check ActivityCashbackSummary
	summary, err := suite.service.GetActivityCashbackSummary(suite.ctx, testUserID)
	suite.NoError(err)

	suite.Equal(expectedVolume, summary.AccumulatedTradingVolumeUSD, "Summary should show accumulated volume")
	suite.T().Log("✅ With the fix, AccumulatedTradingVolumeUSD is correctly accumulated")
}

// TestTradingVolumeAccumulation_MEMEOnly tests volume accumulation with MEME trades only
func (suite *TradingVolumeAccumulationTestSuite) TestTradingVolumeAccumulation_MEMEOnly() {
	// Test that only MEME trades are processed for Activity Cashback

	testUserID := uuid.New()
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err)

	// Test data with different trade types
	trades := []struct {
		tradeType     string
		volume        float64
		shouldProcess bool // Whether this trade should be processed
	}{
		{"MEME", 1000.0, true},         // MEME trades are processed
		{"PERPETUAL", 2000.0, false},   // Derivatives trades are excluded
		{"DERIVATIVES", 1000.0, false}, // Derivatives trades are excluded
	}

	expectedTotalVolume := decimal.Zero

	for _, trade := range trades {
		// Only MEME trades should be processed
		if trade.shouldProcess {
			expectedTotalVolume = expectedTotalVolume.Add(decimal.NewFromFloat(trade.volume))

			// Get current tier info
			tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
			suite.NoError(err)

			// Add trading volume (only MEME trades are processed)
			tierInfo.AddTradingVolume(decimal.NewFromFloat(trade.volume))

			// Update tier info
			err = suite.service.UpdateUserTierInfo(suite.ctx, tierInfo)
			suite.NoError(err)

			suite.T().Logf("Added %s trade: volume=%.2f (processed)",
				trade.tradeType, trade.volume)
		} else {
			suite.T().Logf("Skipped %s trade: volume=%.2f (excluded from Activity Cashback)",
				trade.tradeType, trade.volume)
		}
	}

	// Assert: Check final accumulated volume
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)

	suite.Equal(expectedTotalVolume, finalTierInfo.TradingVolumeUSD,
		"Only MEME trading volume should be accumulated")

	// Expected: Only MEME trade (1000.0) should be counted
	suite.Equal(decimal.NewFromFloat(1000.0), finalTierInfo.TradingVolumeUSD)

	suite.T().Log("✅ MEME-only volume accumulation works correctly")
}

// TestTradingVolumeAccumulation_RealWorldScenario tests with realistic small volumes
func (suite *TradingVolumeAccumulationTestSuite) TestTradingVolumeAccumulation_RealWorldScenario() {
	// Test with realistic small volumes like in the user's actual data

	testUserID := uuid.New()
	err := suite.service.InitializeUserForActivityCashback(suite.ctx, testUserID)
	suite.NoError(err)

	// Realistic small volumes from actual NATS events
	realVolumes := []float64{
		0.002974233, // From affiliate_transactions example
		0.000032864, // From NATS event example
		0.001500000, // Another small trade
		0.000100000, // Very small trade
	}

	expectedTotal := decimal.Zero

	for _, volume := range realVolumes {
		// Get current tier info
		tierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
		suite.NoError(err)

		// Add trading volume
		volumeDecimal := decimal.NewFromFloat(volume)
		tierInfo.AddTradingVolume(volumeDecimal)
		expectedTotal = expectedTotal.Add(volumeDecimal)

		// Update tier info
		err = suite.service.UpdateUserTierInfo(suite.ctx, tierInfo)
		suite.NoError(err)
	}

	// Assert: Check accumulated volume
	finalTierInfo, err := suite.service.GetUserTierInfo(suite.ctx, testUserID)
	suite.NoError(err)

	suite.Equal(expectedTotal, finalTierInfo.TradingVolumeUSD,
		"Small volumes should be accumulated correctly")

	// Check that it's greater than 0
	suite.True(finalTierInfo.TradingVolumeUSD.GreaterThan(decimal.Zero),
		"Accumulated volume should be greater than 0")

	suite.T().Logf("✅ Real-world small volumes accumulated: %s USD",
		finalTierInfo.TradingVolumeUSD.String())
}

// Run the test suite
func TestTradingVolumeAccumulationSuite(t *testing.T) {
	suite.Run(t, new(TradingVolumeAccumulationTestSuite))
}
